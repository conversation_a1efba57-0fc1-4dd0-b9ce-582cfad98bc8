def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '2'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '2.5'
}

apply plugin: 'com.android.application'
//apply plugin: 'com.huawei.agconnect'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
android {
    compileSdkVersion 34

    lintOptions {
        disable 'InvalidPackage'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.getui.getuiflut_example"
        minSdkVersion flutter.minSdkVersion
        targetSdkVersion 33
        manifestPlaceholders = [

                GETUI_APPID     : "djYjSlFVMf6p5YOy2OQUs8",

                XIAOMI_APP_ID   : "",
                XIAOMI_APP_KEY  : "",

                MEIZU_APP_ID    : "",
                MEIZU_APP_KEY   : "",

                HUAWEI_APP_ID   : "",

                OPPO_APP_KEY    : "",
                OPPO_APP_SECRET : "",

                VIVO_APP_ID     : "",
                VIVO_APP_KEY    : ""
        ]
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"


    }

    buildTypes {
        debug {
            minifyEnabled false
            shrinkResources false
           // signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
           // signingConfig signingConfigs.debug
        }
    }

    signingConfigs {
        debug {
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    namespace 'com.getui.getuiflut_example'
}

flutter {
    source '../..'
}
repositories {
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.getui:gtsdk:3.3.7.0'  //个推SDK
    implementation 'com.getui:gtc:3.2.16.0'  //个推核心组件

//    // 根据所需厂商选择集成
//    implementation 'com.getui.opt:hwp:3.1.0'   // 华为
//    implementation 'com.huawei.hms:push:6.1.0.300'

//    implementation 'com.getui.opt:xmp:3.2.0'   // 小米
//    implementation 'com.assist-v3:oppo:3.1.0'  // oppo
//    implementation 'com.assist-v3:vivo:3.1.0'  // vivo
    // 魅族
//    implementation ('com.getui.opt:mzp:3.2.0')
//    implementation 'com.meizu.flyme.internet:push-internal:4.1.4'

}
