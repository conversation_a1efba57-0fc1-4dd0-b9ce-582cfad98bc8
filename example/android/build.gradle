buildscript {
    repositories {
        maven {
            url 'https://maven.aliyun.com/repository/google'
        }
        google()
        jcenter()
        mavenCentral()

        maven {url 'https://developer.huawei.com/repo/'}

    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.2'
        classpath 'com.huawei.agconnect:agcp:1.6.0.300'
    }
}

allprojects {
    repositories {
        maven {
            url 'https://maven.aliyun.com/repository/google'
        }
        google()
        jcenter()
        mavenCentral()
        maven {url 'https://developer.huawei.com/repo/'}
        maven {
            url 'https://maven.columbus.heytapmobi.com/repository/releases/'
            credentials {
                username 'nexus'
                password 'c0b08da17e3ec36c3870fed674a0bcb36abc2e23'
            }
        }
        maven {
            url 'https://maven.columbus.heytapmobi.com/repository/releases/'
            credentials {
                username 'nexus'
                password 'c0b08da17e3ec36c3870fed674a0bcb36abc2e23'
            }
        }
        maven {
            url "https://mvn.getui.com/nexus/content/repositories/releases/"
        }

    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
