<?xml version="1.0" encoding="utf-8" standalone="no"?>
<resources>
    <string name="app_title">个推演示</string>
    <string name="tab_one">通知栏/透传测试</string>
    <string name="tab_two">高级功能</string>
    <string name="tab_three">应用信息</string>
    <string name="app_name">个推Demo</string>
    <string name="no_clientid">(none)</string>
    <string name="start">已启动</string>
    <string name="clientid">ClientID: </string>
    <string name="stop">已停止</string>
    <string name="pmsg">透传测试</string>
    <string name="psmsg">通知测试</string>
    <string name="transmit">透传内容</string>
    <string name="begin">开始时间(beginHour)</string>
    <string name="duration">持续时间(durationHour)</string>
    <string name="bind_alias">绑定别名</string>
    <string name="bind_alias_unknown_exception">绑定别名失败，未知异常</string>
    <string name="bind_alias_success">绑定别名成功</string>
    <string name="bind_alias_error_frequency">绑定别名失败，请求频次超限</string>
    <string name="bind_alias_error_param_error">绑定别名失败，参数错误</string>
    <string name="bind_alias_error_request_filter">绑定别名失败，请求被过滤</string>
    <string name="bind_alias_error_cid_lost">绑定别名失败，未获取到cid</string>
    <string name="bind_alias_error_connect_lost">绑定别名失败，网络错误</string>
    <string name="bind_alias_error_alias_invalid">绑定别名失败，别名无效</string>
    <string name="bind_alias_error_sn_invalid">绑定别名失败，sn无效</string>
    <string name="unbind_alias">解绑别名</string>
    <string name="unbind_alias_unknown_exception">取消绑定别名失败，未知异常</string>
    <string name="unbind_alias_success">取消绑定别名成功</string>
    <string name="unbind_alias_error_frequency">取消绑定别名失败，请求频次超限</string>
    <string name="unbind_alias_error_param_error">取消绑定别名失败，参数错误</string>
    <string name="unbind_alias_error_request_filter">取消绑定别名失败，请求被过滤</string>
    <string name="unbind_alias_error_cid_lost">取消绑定别名失败，未获取到cid</string>
    <string name="unbind_alias_error_connect_lost">取消绑定别名失败，网络错误</string>
    <string name="unbind_alias_error_alias_invalid">取消绑定别名失败，别名无效</string>
    <string name="unbind_alias_error_sn_invalid">取消绑定别名失败，sn无效</string>
    <string name="add_tag">添加Tag</string>
    <string name="add_tag_unknown_exception">设置标签失败，未知异常</string>
    <string name="add_tag_success">接口调用成功</string>
    <string name="add_tag_error_count">接口调用失败, tag数量过大, 最大不能超过200个</string>
    <string name="add_tag_error_frequency">设置标签失败，超出频次限制</string>
    <string name="add_tag_error_null">接口调用失败, tag 为空</string>
    <string name="add_tag_sn_null">接口调用失败, sn 为空</string>
    <string name="add_tag_error_repeat">设置标签失败, 标签重复</string>
    <string name="add_tag_error_unbind">设置标签失败, 服务未初始化成功</string>
    <string name="add_tag_error_not_online">还未登陆成功</string>
    <string name="add_tag_error_black_list">该应用已经在黑名单中,请联系售后支持!</string>
    <string name="add_tag_error_exceed">已存 tag 超过限制</string>
    <string name="add_tag_error_tagIllegal">TAG不合法，请检查！</string>
    <string name="confirm_setting">确定设置</string>
    <string name="push_notification_title">通知栏测试</string>
    <string name="push_notification_msg_title">通知栏测试</string>
    <string name="push_notification_msg_content">您收到一条测试消息，点击访问</string>
    <string name="push_transmission_data">收到一条透传测试消息</string>
    <string name="network_invalid">对不起，当前网络不可用!</string>
    <string name="bind_alias_hint">40字节以内，支持中，英文（区分大小写）、数字以及下划线，每次设置都会覆盖之前的别名</string>
    <string name="unbind_alias_hint">输入已绑定过的别名，将已绑定的别名解绑</string>
    <string name="silent_begin_hint">0-23(单位小时)</string>
    <string name="silent_duration_hint">0-23(单位小时),为0则不静默</string>
    <string name="online">在线</string>
    <string name="offline">离线</string>
    <string name="log_cleared">日志已清除</string>
    <string name="cid_empty">CID为空</string>
    <string name="copy_successed">复制成功</string>
    <string name="send_successed">发送成功</string>
    <string name="confirm_bind">确认绑定</string>
    <string name="confirm_unbind">确认解绑</string>
    <string name="input_alias">请输入别名</string>
    <string name="bind_request_sended">绑定请求已发送</string>
    <string name="unbind_request_sended">解除绑定请求已发送</string>
    <string name="check_silent_time_format">时间格式错误，请检查格式</string>
    <string name="silent_time_config">静默时间设置</string>
    <string name="set_tag">Tag设置</string>
    <string name="tag_too_long">Tag长度不能大于40</string>
    <string name="tag_empty">Tag为空</string>
    <string name="confirm">确定设置</string>
    <string name="input_tag">请输入Tag名</string>
    <string name="tag_format_hint">支持中文、英文字母、数字、除英文逗号以外的其他特殊符号</string>
    <string name="sdk_server">SDK服务:</string>
    <string name="online_state">在线/离线</string>
    <string name="cid_state">CID状态:</string>
    <string name="copy">复制</string>
    <string name="app">应用名称</string>
    <string name="packageName">包名</string>
    <string name="sdk_ver">SDK版本号</string>
    <string name="tag_des">标签是用户的一种属性，设置标签后即可针对用户推送，默认套餐一天仅能设置一次</string>
    <string name="alias_instruction">对已安装某应用的用户取别名来标识，对该用户消息推送时，可用此别名来推送。</string>
    <string name="set_silent_time_instruction">调用接口设置静默时间，在静默时间期间SDK将不再联网</string>

    <string name="notification_setting_title">温馨提示</string>
    <string name="notification_setting_hint">检测到您的通知权限已关闭，暂无法接收通知消息，请前往开启</string>
    <string name="cancel">取消</string>
    <string name="go_set">去设置</string>
</resources>
